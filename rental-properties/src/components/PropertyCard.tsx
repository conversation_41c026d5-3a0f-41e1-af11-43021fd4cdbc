'use client'

import { useState } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { MapPin, Eye, DollarSign, Square } from 'lucide-react'
import { supabase } from '@/lib/supabase'
import toast from 'react-hot-toast'
import AuthModal from './AuthModal'

interface Property {
  id: string
  title: string
  description: string | null
  area: string
  price: number
  size_sqft: number | null
  images: string[]
  view_count: number
  created_at: string
  user_id: string
  user_name: string | null
}

interface PropertyCardProps {
  property: Property
}

export default function PropertyCard({ property }: PropertyCardProps) {
  const { user } = useAuth()
  const [showAuthModal, setShowAuthModal] = useState(false)
  const [loading, setLoading] = useState(false)

  const handleViewProperty = async () => {
    try {
      // Increment view count
      await supabase.rpc('increment_property_view', {
        property_uuid: property.id,
        user_uuid: user?.id || null,
        user_ip: null // We'll handle IP on the server side if needed
      })
    } catch (error) {
      console.error('Error incrementing view:', error)
    }
  }

  const handleContactSeller = async () => {
    if (!user) {
      setShowAuthModal(true)
      return
    }

    if (user.id === property.user_id) {
      toast.error("You can't contact yourself!")
      return
    }

    setLoading(true)
    try {
      // Check if chat already exists
      const { data: existingChat } = await supabase
        .from('chats')
        .select('id')
        .eq('property_id', property.id)
        .eq('buyer_id', user.id)
        .single()

      if (existingChat) {
        // Redirect to existing chat
        window.location.href = `/dashboard/chats/${existingChat.id}`
      } else {
        // Create new chat
        const { data: newChat, error } = await supabase
          .from('chats')
          .insert({
            property_id: property.id,
            buyer_id: user.id,
            seller_id: property.user_id
          })
          .select('id')
          .single()

        if (error) throw error

        if (newChat) {
          // Send initial message
          await supabase
            .from('messages')
            .insert({
              chat_id: newChat.id,
              sender_id: user.id,
              content: `Hi! I'm interested in your property: ${property.title}`
            })

          toast.success('Chat started successfully!')
          window.location.href = `/dashboard/chats/${newChat.id}`
        }
      }
    } catch (error) {
      console.error('Error creating chat:', error)
      toast.error('Failed to start chat')
    } finally {
      setLoading(false)
    }
  }

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(price)
  }

  return (
    <>
      <div 
        className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow cursor-pointer"
        onClick={handleViewProperty}
      >
        {/* Property Image */}
        <div className="h-48 bg-gray-200 relative">
          {property.images && property.images.length > 0 ? (
            <img
              src={property.images[0]}
              alt={property.title}
              className="w-full h-full object-cover"
            />
          ) : (
            <div className="w-full h-full flex items-center justify-center text-gray-400">
              <Square className="h-12 w-12" />
            </div>
          )}
          <div className="absolute top-2 right-2 bg-black bg-opacity-50 text-white px-2 py-1 rounded text-sm flex items-center">
            <Eye className="h-3 w-3 mr-1" />
            {property.view_count}
          </div>
        </div>

        {/* Property Details */}
        <div className="p-4">
          <h3 className="text-lg font-semibold text-gray-900 mb-2 line-clamp-1">
            {property.title}
          </h3>
          
          <div className="flex items-center text-gray-600 mb-2">
            <MapPin className="h-4 w-4 mr-1" />
            <span className="text-sm">{property.area}</span>
          </div>

          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center text-green-600 font-semibold">
              <DollarSign className="h-4 w-4" />
              <span>{formatPrice(property.price)}/month</span>
            </div>
            {property.size_sqft && (
              <div className="flex items-center text-gray-600 text-sm">
                <Square className="h-4 w-4 mr-1" />
                <span>{property.size_sqft} sqft</span>
              </div>
            )}
          </div>

          {property.description && (
            <p className="text-gray-600 text-sm mb-3 line-clamp-2">
              {property.description}
            </p>
          )}

          <div className="flex items-center justify-between">
            <span className="text-xs text-gray-500">
              Listed by {property.user_name || 'Anonymous'}
            </span>
            <button
              onClick={(e) => {
                e.stopPropagation()
                handleContactSeller()
              }}
              disabled={loading}
              className="bg-blue-600 text-white px-4 py-2 rounded text-sm hover:bg-blue-700 disabled:opacity-50"
            >
              {loading ? 'Loading...' : 'Contact Now'}
            </button>
          </div>
        </div>
      </div>

      <AuthModal isOpen={showAuthModal} onClose={() => setShowAuthModal(false)} />
    </>
  )
}
