'use client'

import { useState, useEffect, useRef } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { supabase } from '@/lib/supabase'
import { useParams, useRouter } from 'next/navigation'
import { Send, ArrowLeft, User, Home } from 'lucide-react'
import Link from 'next/link'
import toast from 'react-hot-toast'

interface Message {
  id: string
  chat_id: string
  sender_id: string
  content: string
  created_at: string
}

interface Chat {
  id: string
  property_id: string
  buyer_id: string
  seller_id: string
  property: {
    title: string
    price: number
    images: string[]
    area: string
  }
  buyer: {
    full_name: string | null
    email: string
  }
  seller: {
    full_name: string | null
    email: string
  }
}

export default function ChatDetail() {
  const { user } = useAuth()
  const params = useParams()
  const router = useRouter()
  const chatId = params.id as string
  
  const [chat, setChat] = useState<Chat | null>(null)
  const [messages, setMessages] = useState<Message[]>([])
  const [newMessage, setNewMessage] = useState('')
  const [loading, setLoading] = useState(true)
  const [sending, setSending] = useState(false)
  const messagesEndRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    if (chatId && user) {
      fetchChatData()
      fetchMessages()
      
      // Subscribe to new messages
      const subscription = supabase
        .channel(`chat-${chatId}`)
        .on(
          'postgres_changes',
          {
            event: 'INSERT',
            schema: 'public',
            table: 'messages',
            filter: `chat_id=eq.${chatId}`,
          },
          (payload) => {
            setMessages(prev => [...prev, payload.new as Message])
          }
        )
        .subscribe()

      return () => {
        subscription.unsubscribe()
      }
    }
  }, [chatId, user])

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  const fetchChatData = async () => {
    try {
      const { data, error } = await supabase
        .from('chats')
        .select(`
          *,
          property:properties!chats_property_id_fkey(title, price, images, area),
          buyer:users!chats_buyer_id_fkey(full_name, email),
          seller:users!chats_seller_id_fkey(full_name, email)
        `)
        .eq('id', chatId)
        .single()

      if (error) throw error

      // Check if user is part of this chat
      if (data.buyer_id !== user?.id && data.seller_id !== user?.id) {
        router.push('/dashboard/chats')
        return
      }

      setChat(data)
    } catch (error) {
      console.error('Error fetching chat:', error)
      router.push('/dashboard/chats')
    }
  }

  const fetchMessages = async () => {
    try {
      const { data, error } = await supabase
        .from('messages')
        .select('*')
        .eq('chat_id', chatId)
        .order('created_at', { ascending: true })

      if (error) throw error
      setMessages(data || [])
    } catch (error) {
      console.error('Error fetching messages:', error)
    } finally {
      setLoading(false)
    }
  }

  const sendMessage = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!newMessage.trim() || !user || !chat) return

    setSending(true)
    try {
      const { error } = await supabase
        .from('messages')
        .insert({
          chat_id: chatId,
          sender_id: user.id,
          content: newMessage.trim(),
        })

      if (error) throw error

      // Update chat's updated_at timestamp
      await supabase
        .from('chats')
        .update({ updated_at: new Date().toISOString() })
        .eq('id', chatId)

      setNewMessage('')
    } catch (error) {
      console.error('Error sending message:', error)
      toast.error('Failed to send message')
    } finally {
      setSending(false)
    }
  }

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  const formatTime = (dateString: string) => {
    return new Date(dateString).toLocaleTimeString([], { 
      hour: '2-digit', 
      minute: '2-digit' 
    })
  }

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(price)
  }

  const getOtherUser = () => {
    if (!chat || !user) return null
    return chat.buyer_id === user.id ? chat.seller : chat.buyer
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (!chat) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-600">Chat not found</p>
        <Link href="/dashboard/chats" className="text-blue-600 hover:text-blue-800">
          Back to Chats
        </Link>
      </div>
    )
  }

  const otherUser = getOtherUser()

  return (
    <div className="max-w-4xl mx-auto">
      {/* Header */}
      <div className="bg-white rounded-lg shadow mb-6">
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <Link
              href="/dashboard/chats"
              className="flex items-center text-gray-600 hover:text-gray-900"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Chats
            </Link>
          </div>
          
          <div className="mt-4 flex items-start space-x-4">
            {/* Property Image */}
            <div className="flex-shrink-0">
              {chat.property.images && chat.property.images.length > 0 ? (
                <img
                  src={chat.property.images[0]}
                  alt={chat.property.title}
                  className="w-20 h-20 rounded-lg object-cover"
                />
              ) : (
                <div className="w-20 h-20 bg-gray-200 rounded-lg flex items-center justify-center">
                  <Home className="h-8 w-8 text-gray-400" />
                </div>
              )}
            </div>

            {/* Property & User Info */}
            <div className="flex-1">
              <h1 className="text-xl font-bold text-gray-900 mb-1">
                {chat.property.title}
              </h1>
              <p className="text-gray-600 mb-2">{chat.property.area}</p>
              <p className="text-lg font-semibold text-green-600 mb-2">
                {formatPrice(chat.property.price)}/month
              </p>
              <div className="flex items-center text-sm text-gray-600">
                <User className="h-4 w-4 mr-1" />
                <span>
                  Chatting with {otherUser?.full_name || otherUser?.email}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Messages */}
      <div className="bg-white rounded-lg shadow flex flex-col h-96">
        <div className="flex-1 overflow-y-auto p-6 space-y-4">
          {messages.map((message) => {
            const isFromCurrentUser = message.sender_id === user?.id
            
            return (
              <div
                key={message.id}
                className={`flex ${isFromCurrentUser ? 'justify-end' : 'justify-start'}`}
              >
                <div
                  className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                    isFromCurrentUser
                      ? 'bg-blue-600 text-white'
                      : 'bg-gray-200 text-gray-900'
                  }`}
                >
                  <p className="text-sm">{message.content}</p>
                  <p
                    className={`text-xs mt-1 ${
                      isFromCurrentUser ? 'text-blue-100' : 'text-gray-500'
                    }`}
                  >
                    {formatTime(message.created_at)}
                  </p>
                </div>
              </div>
            )
          })}
          <div ref={messagesEndRef} />
        </div>

        {/* Message Input */}
        <div className="border-t border-gray-200 p-4">
          <form onSubmit={sendMessage} className="flex space-x-2">
            <input
              type="text"
              value={newMessage}
              onChange={(e) => setNewMessage(e.target.value)}
              placeholder="Type your message..."
              className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              disabled={sending}
            />
            <button
              type="submit"
              disabled={sending || !newMessage.trim()}
              className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 disabled:opacity-50 flex items-center"
            >
              <Send className="h-4 w-4" />
            </button>
          </form>
        </div>
      </div>
    </div>
  )
}
