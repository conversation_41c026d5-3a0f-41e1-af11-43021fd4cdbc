{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/rental/rental-properties/src/lib/supabase.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js'\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!\n\nexport const supabase = createClient(supabaseUrl, supabaseAnonKey)\n\nexport type Database = {\n  public: {\n    Tables: {\n      users: {\n        Row: {\n          id: string\n          email: string\n          full_name: string | null\n          avatar_url: string | null\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          id: string\n          email: string\n          full_name?: string | null\n          avatar_url?: string | null\n          created_at?: string\n          updated_at?: string\n        }\n        Update: {\n          id?: string\n          email?: string\n          full_name?: string | null\n          avatar_url?: string | null\n          created_at?: string\n          updated_at?: string\n        }\n      }\n      properties: {\n        Row: {\n          id: string\n          user_id: string\n          title: string\n          description: string | null\n          area: string\n          price: number\n          size_sqft: number | null\n          images: string[]\n          view_count: number\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          id?: string\n          user_id: string\n          title: string\n          description?: string | null\n          area: string\n          price: number\n          size_sqft?: number | null\n          images?: string[]\n          view_count?: number\n          created_at?: string\n          updated_at?: string\n        }\n        Update: {\n          id?: string\n          user_id?: string\n          title?: string\n          description?: string | null\n          area?: string\n          price?: number\n          size_sqft?: number | null\n          images?: string[]\n          view_count?: number\n          created_at?: string\n          updated_at?: string\n        }\n      }\n      chats: {\n        Row: {\n          id: string\n          property_id: string\n          buyer_id: string\n          seller_id: string\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          id?: string\n          property_id: string\n          buyer_id: string\n          seller_id: string\n          created_at?: string\n          updated_at?: string\n        }\n        Update: {\n          id?: string\n          property_id?: string\n          buyer_id?: string\n          seller_id?: string\n          created_at?: string\n          updated_at?: string\n        }\n      }\n      messages: {\n        Row: {\n          id: string\n          chat_id: string\n          sender_id: string\n          content: string\n          created_at: string\n        }\n        Insert: {\n          id?: string\n          chat_id: string\n          sender_id: string\n          content: string\n          created_at?: string\n        }\n        Update: {\n          id?: string\n          chat_id?: string\n          sender_id?: string\n          content?: string\n          created_at?: string\n        }\n      }\n      property_views: {\n        Row: {\n          id: string\n          property_id: string\n          user_id: string | null\n          ip_address: string | null\n          created_at: string\n        }\n        Insert: {\n          id?: string\n          property_id: string\n          user_id?: string | null\n          ip_address?: string | null\n          created_at?: string\n        }\n        Update: {\n          id?: string\n          property_id?: string\n          user_id?: string | null\n          ip_address?: string | null\n          created_at?: string\n        }\n      }\n    }\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM;AACN,MAAM;AAEC,MAAM,WAAW,CAAA,GAAA,uLAAA,CAAA,eAAY,AAAD,EAAE,aAAa", "debugId": null}}, {"offset": {"line": 21, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/rental/rental-properties/src/app/dashboard/add-property/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useRef } from 'react'\nimport { useAuth } from '@/contexts/AuthContext'\nimport { supabase } from '@/lib/supabase'\nimport { useRouter } from 'next/navigation'\nimport { Upload, X, Plus, Image as ImageIcon } from 'lucide-react'\nimport toast from 'react-hot-toast'\n\nexport default function AddProperty() {\n  const { user } = useAuth()\n  const router = useRouter()\n  const [loading, setLoading] = useState(false)\n  const [uploading, setUploading] = useState(false)\n  const fileInputRef = useRef<HTMLInputElement>(null)\n  const [formData, setFormData] = useState({\n    title: '',\n    description: '',\n    area: '',\n    price: '',\n    size_sqft: '',\n    images: [] as string[],\n  })\n\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {\n    const { name, value } = e.target\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }))\n  }\n\n  const handleFileUpload = async (files: FileList) => {\n    if (!user) return\n\n    setUploading(true)\n    const uploadedUrls: string[] = []\n\n    try {\n      for (let i = 0; i < files.length; i++) {\n        const file = files[i]\n\n        // Check file type\n        if (!file.type.startsWith('image/')) {\n          toast.error(`${file.name} is not an image file`)\n          continue\n        }\n\n        // Check file size (max 5MB)\n        if (file.size > 5 * 1024 * 1024) {\n          toast.error(`${file.name} is too large (max 5MB)`)\n          continue\n        }\n\n        // Create unique filename\n        const fileExt = file.name.split('.').pop()\n        const fileName = `${user.id}/${Date.now()}-${Math.random().toString(36).substring(2)}.${fileExt}`\n\n        // Upload to Supabase Storage\n        const { data, error } = await supabase.storage\n          .from('property-images')\n          .upload(fileName, file)\n\n        if (error) {\n          console.error('Upload error:', error)\n          toast.error(`Failed to upload ${file.name}`)\n          continue\n        }\n\n        // Get public URL\n        const { data: { publicUrl } } = supabase.storage\n          .from('property-images')\n          .getPublicUrl(fileName)\n\n        uploadedUrls.push(publicUrl)\n      }\n\n      if (uploadedUrls.length > 0) {\n        setFormData(prev => ({\n          ...prev,\n          images: [...prev.images, ...uploadedUrls]\n        }))\n        toast.success(`${uploadedUrls.length} image(s) uploaded successfully`)\n      }\n    } catch (error) {\n      console.error('Error uploading files:', error)\n      toast.error('Failed to upload images')\n    } finally {\n      setUploading(false)\n    }\n  }\n\n  const handleImageAdd = () => {\n    const imageUrl = prompt('Enter image URL:')\n    if (imageUrl && imageUrl.trim()) {\n      setFormData(prev => ({\n        ...prev,\n        images: [...prev.images, imageUrl.trim()]\n      }))\n    }\n  }\n\n  const handleImageRemove = async (index: number) => {\n    const imageUrl = formData.images[index]\n\n    // If it's a Supabase storage URL, delete from storage\n    if (imageUrl.includes('supabase.co/storage/v1/object/public/property-images/')) {\n      try {\n        const fileName = imageUrl.split('/property-images/')[1]\n        await supabase.storage\n          .from('property-images')\n          .remove([fileName])\n      } catch (error) {\n        console.error('Error deleting image from storage:', error)\n      }\n    }\n\n    setFormData(prev => ({\n      ...prev,\n      images: prev.images.filter((_, i) => i !== index)\n    }))\n  }\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault()\n\n    if (!user) {\n      toast.error('You must be logged in to add a property')\n      return\n    }\n\n    if (!formData.title || !formData.area || !formData.price) {\n      toast.error('Please fill in all required fields')\n      return\n    }\n\n    const price = parseFloat(formData.price)\n    if (isNaN(price) || price <= 0) {\n      toast.error('Please enter a valid price')\n      return\n    }\n\n    const sizeSqft = formData.size_sqft ? parseInt(formData.size_sqft) : null\n    if (formData.size_sqft && (isNaN(sizeSqft!) || sizeSqft! <= 0)) {\n      toast.error('Please enter a valid size')\n      return\n    }\n\n    setLoading(true)\n    try {\n      const { data, error } = await supabase\n        .from('properties')\n        .insert({\n          user_id: user.id,\n          title: formData.title.trim(),\n          description: formData.description?.trim() || null,\n          area: formData.area.trim(),\n          price: price,\n          size_sqft: sizeSqft,\n          images: formData.images,\n        })\n        .select()\n\n      if (error) {\n        console.error('Supabase error:', error)\n        throw error\n      }\n\n      toast.success('Property added successfully!')\n      router.push('/dashboard/my-listings')\n    } catch (error: any) {\n      console.error('Error adding property:', error)\n      toast.error(error.message || 'Failed to add property')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  return (\n    <div className=\"max-w-2xl mx-auto\">\n      <div className=\"bg-white rounded-lg shadow p-6\">\n        <h1 className=\"text-2xl font-bold text-gray-900 mb-6\">Add New Property</h1>\n        \n        <form onSubmit={handleSubmit} className=\"space-y-6\">\n          {/* Title */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              Property Title *\n            </label>\n            <input\n              type=\"text\"\n              name=\"title\"\n              value={formData.title}\n              onChange={handleInputChange}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n              placeholder=\"e.g., Beautiful 2BR Apartment in Downtown\"\n              required\n            />\n          </div>\n\n          {/* Area */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              Area/Location *\n            </label>\n            <input\n              type=\"text\"\n              name=\"area\"\n              value={formData.area}\n              onChange={handleInputChange}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n              placeholder=\"e.g., Downtown, Manhattan, NY\"\n              required\n            />\n          </div>\n\n          {/* Price and Size */}\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Monthly Rent ($) *\n              </label>\n              <input\n                type=\"number\"\n                name=\"price\"\n                value={formData.price}\n                onChange={handleInputChange}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                placeholder=\"2500\"\n                min=\"0\"\n                step=\"0.01\"\n                required\n              />\n            </div>\n            \n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Size (sq ft)\n              </label>\n              <input\n                type=\"number\"\n                name=\"size_sqft\"\n                value={formData.size_sqft}\n                onChange={handleInputChange}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                placeholder=\"1200\"\n                min=\"0\"\n              />\n            </div>\n          </div>\n\n          {/* Description */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              Description\n            </label>\n            <textarea\n              name=\"description\"\n              value={formData.description}\n              onChange={handleInputChange}\n              rows={4}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n              placeholder=\"Describe your property, amenities, nearby attractions, etc.\"\n            />\n          </div>\n\n          {/* Images */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              Property Images\n            </label>\n\n            {/* Image List */}\n            {formData.images.length > 0 && (\n              <div className=\"grid grid-cols-2 md:grid-cols-3 gap-4 mb-4\">\n                {formData.images.map((image, index) => (\n                  <div key={index} className=\"relative\">\n                    <img\n                      src={image}\n                      alt={`Property ${index + 1}`}\n                      className=\"w-full h-24 object-cover rounded-md border\"\n                    />\n                    <button\n                      type=\"button\"\n                      onClick={() => handleImageRemove(index)}\n                      className=\"absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600\"\n                    >\n                      <X className=\"h-3 w-3\" />\n                    </button>\n                  </div>\n                ))}\n              </div>\n            )}\n\n            {/* Upload Buttons */}\n            <div className=\"space-y-2\">\n              <input\n                type=\"file\"\n                ref={fileInputRef}\n                onChange={(e) => e.target.files && handleFileUpload(e.target.files)}\n                accept=\"image/*\"\n                multiple\n                className=\"hidden\"\n              />\n\n              <button\n                type=\"button\"\n                onClick={() => fileInputRef.current?.click()}\n                disabled={uploading}\n                className=\"flex items-center justify-center w-full h-24 border-2 border-dashed border-gray-300 rounded-md hover:border-gray-400 transition-colors disabled:opacity-50\"\n              >\n                <div className=\"text-center\">\n                  {uploading ? (\n                    <>\n                      <div className=\"animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mx-auto mb-1\"></div>\n                      <span className=\"text-sm text-gray-600\">Uploading...</span>\n                    </>\n                  ) : (\n                    <>\n                      <Upload className=\"h-6 w-6 text-gray-400 mx-auto mb-1\" />\n                      <span className=\"text-sm text-gray-600\">Upload Images</span>\n                    </>\n                  )}\n                </div>\n              </button>\n\n              <button\n                type=\"button\"\n                onClick={handleImageAdd}\n                className=\"flex items-center justify-center w-full h-12 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors\"\n              >\n                <ImageIcon className=\"h-4 w-4 text-gray-400 mr-2\" />\n                <span className=\"text-sm text-gray-600\">Add Image URL</span>\n              </button>\n            </div>\n\n            <p className=\"text-xs text-gray-500 mt-2\">\n              Upload images (max 5MB each) or add image URLs. You can add multiple images.\n            </p>\n          </div>\n\n          {/* Submit Button */}\n          <div className=\"flex justify-end space-x-4\">\n            <button\n              type=\"button\"\n              onClick={() => router.back()}\n              className=\"px-4 py-2 text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300\"\n            >\n              Cancel\n            </button>\n            <button\n              type=\"submit\"\n              disabled={loading}\n              className=\"px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50\"\n            >\n              {loading ? 'Adding...' : 'Add Property'}\n            </button>\n          </div>\n        </form>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AAPA;;;;;;;;AASe,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IACvB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoB;IAC9C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,OAAO;QACP,aAAa;QACb,MAAM;QACN,OAAO;QACP,WAAW;QACX,QAAQ,EAAE;IACZ;IAEA,MAAM,oBAAoB,CAAC;QACzB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;QAChC,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,KAAK,EAAE;YACV,CAAC;IACH;IAEA,MAAM,mBAAmB,OAAO;QAC9B,IAAI,CAAC,MAAM;QAEX,aAAa;QACb,MAAM,eAAyB,EAAE;QAEjC,IAAI;YACF,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;gBACrC,MAAM,OAAO,KAAK,CAAC,EAAE;gBAErB,kBAAkB;gBAClB,IAAI,CAAC,KAAK,IAAI,CAAC,UAAU,CAAC,WAAW;oBACnC,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC,GAAG,KAAK,IAAI,CAAC,qBAAqB,CAAC;oBAC/C;gBACF;gBAEA,4BAA4B;gBAC5B,IAAI,KAAK,IAAI,GAAG,IAAI,OAAO,MAAM;oBAC/B,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC,GAAG,KAAK,IAAI,CAAC,uBAAuB,CAAC;oBACjD;gBACF;gBAEA,yBAAyB;gBACzB,MAAM,UAAU,KAAK,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG;gBACxC,MAAM,WAAW,GAAG,KAAK,EAAE,CAAC,CAAC,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG,CAAC,EAAE,SAAS;gBAEjG,6BAA6B;gBAC7B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAAC,OAAO,CAC3C,IAAI,CAAC,mBACL,MAAM,CAAC,UAAU;gBAEpB,IAAI,OAAO;oBACT,QAAQ,KAAK,CAAC,iBAAiB;oBAC/B,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC,CAAC,iBAAiB,EAAE,KAAK,IAAI,EAAE;oBAC3C;gBACF;gBAEA,iBAAiB;gBACjB,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,EAAE,GAAG,sHAAA,CAAA,WAAQ,CAAC,OAAO,CAC7C,IAAI,CAAC,mBACL,YAAY,CAAC;gBAEhB,aAAa,IAAI,CAAC;YACpB;YAEA,IAAI,aAAa,MAAM,GAAG,GAAG;gBAC3B,YAAY,CAAA,OAAQ,CAAC;wBACnB,GAAG,IAAI;wBACP,QAAQ;+BAAI,KAAK,MAAM;+BAAK;yBAAa;oBAC3C,CAAC;gBACD,uJAAA,CAAA,UAAK,CAAC,OAAO,CAAC,GAAG,aAAa,MAAM,CAAC,+BAA+B,CAAC;YACvE;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,iBAAiB;QACrB,MAAM,WAAW,OAAO;QACxB,IAAI,YAAY,SAAS,IAAI,IAAI;YAC/B,YAAY,CAAA,OAAQ,CAAC;oBACnB,GAAG,IAAI;oBACP,QAAQ;2BAAI,KAAK,MAAM;wBAAE,SAAS,IAAI;qBAAG;gBAC3C,CAAC;QACH;IACF;IAEA,MAAM,oBAAoB,OAAO;QAC/B,MAAM,WAAW,SAAS,MAAM,CAAC,MAAM;QAEvC,sDAAsD;QACtD,IAAI,SAAS,QAAQ,CAAC,0DAA0D;YAC9E,IAAI;gBACF,MAAM,WAAW,SAAS,KAAK,CAAC,oBAAoB,CAAC,EAAE;gBACvD,MAAM,sHAAA,CAAA,WAAQ,CAAC,OAAO,CACnB,IAAI,CAAC,mBACL,MAAM,CAAC;oBAAC;iBAAS;YACtB,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,sCAAsC;YACtD;QACF;QAEA,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,QAAQ,KAAK,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,IAAM,MAAM;YAC7C,CAAC;IACH;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI,CAAC,MAAM;YACT,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,IAAI,CAAC,SAAS,KAAK,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,KAAK,EAAE;YACxD,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,MAAM,QAAQ,WAAW,SAAS,KAAK;QACvC,IAAI,MAAM,UAAU,SAAS,GAAG;YAC9B,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,MAAM,WAAW,SAAS,SAAS,GAAG,SAAS,SAAS,SAAS,IAAI;QACrE,IAAI,SAAS,SAAS,IAAI,CAAC,MAAM,aAAc,YAAa,CAAC,GAAG;YAC9D,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,WAAW;QACX,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,cACL,MAAM,CAAC;gBACN,SAAS,KAAK,EAAE;gBAChB,OAAO,SAAS,KAAK,CAAC,IAAI;gBAC1B,aAAa,SAAS,WAAW,EAAE,UAAU;gBAC7C,MAAM,SAAS,IAAI,CAAC,IAAI;gBACxB,OAAO;gBACP,WAAW;gBACX,QAAQ,SAAS,MAAM;YACzB,GACC,MAAM;YAET,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,mBAAmB;gBACjC,MAAM;YACR;YAEA,uJAAA,CAAA,UAAK,CAAC,OAAO,CAAC;YACd,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,0BAA0B;YACxC,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC,MAAM,OAAO,IAAI;QAC/B,SAAU;YACR,WAAW;QACb;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAG,WAAU;8BAAwC;;;;;;8BAEtD,8OAAC;oBAAK,UAAU;oBAAc,WAAU;;sCAEtC,8OAAC;;8CACC,8OAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,8OAAC;oCACC,MAAK;oCACL,MAAK;oCACL,OAAO,SAAS,KAAK;oCACrB,UAAU;oCACV,WAAU;oCACV,aAAY;oCACZ,QAAQ;;;;;;;;;;;;sCAKZ,8OAAC;;8CACC,8OAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,8OAAC;oCACC,MAAK;oCACL,MAAK;oCACL,OAAO,SAAS,IAAI;oCACpB,UAAU;oCACV,WAAU;oCACV,aAAY;oCACZ,QAAQ;;;;;;;;;;;;sCAKZ,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAGhE,8OAAC;4CACC,MAAK;4CACL,MAAK;4CACL,OAAO,SAAS,KAAK;4CACrB,UAAU;4CACV,WAAU;4CACV,aAAY;4CACZ,KAAI;4CACJ,MAAK;4CACL,QAAQ;;;;;;;;;;;;8CAIZ,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAGhE,8OAAC;4CACC,MAAK;4CACL,MAAK;4CACL,OAAO,SAAS,SAAS;4CACzB,UAAU;4CACV,WAAU;4CACV,aAAY;4CACZ,KAAI;;;;;;;;;;;;;;;;;;sCAMV,8OAAC;;8CACC,8OAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,8OAAC;oCACC,MAAK;oCACL,OAAO,SAAS,WAAW;oCAC3B,UAAU;oCACV,MAAM;oCACN,WAAU;oCACV,aAAY;;;;;;;;;;;;sCAKhB,8OAAC;;8CACC,8OAAC;oCAAM,WAAU;8CAA+C;;;;;;gCAK/D,SAAS,MAAM,CAAC,MAAM,GAAG,mBACxB,8OAAC;oCAAI,WAAU;8CACZ,SAAS,MAAM,CAAC,GAAG,CAAC,CAAC,OAAO,sBAC3B,8OAAC;4CAAgB,WAAU;;8DACzB,8OAAC;oDACC,KAAK;oDACL,KAAK,CAAC,SAAS,EAAE,QAAQ,GAAG;oDAC5B,WAAU;;;;;;8DAEZ,8OAAC;oDACC,MAAK;oDACL,SAAS,IAAM,kBAAkB;oDACjC,WAAU;8DAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;wDAAC,WAAU;;;;;;;;;;;;2CAXP;;;;;;;;;;8CAmBhB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,MAAK;4CACL,KAAK;4CACL,UAAU,CAAC,IAAM,EAAE,MAAM,CAAC,KAAK,IAAI,iBAAiB,EAAE,MAAM,CAAC,KAAK;4CAClE,QAAO;4CACP,QAAQ;4CACR,WAAU;;;;;;sDAGZ,8OAAC;4CACC,MAAK;4CACL,SAAS,IAAM,aAAa,OAAO,EAAE;4CACrC,UAAU;4CACV,WAAU;sDAEV,cAAA,8OAAC;gDAAI,WAAU;0DACZ,0BACC;;sEACE,8OAAC;4DAAI,WAAU;;;;;;sEACf,8OAAC;4DAAK,WAAU;sEAAwB;;;;;;;iFAG1C;;sEACE,8OAAC,sMAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;sEAClB,8OAAC;4DAAK,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;sDAMhD,8OAAC;4CACC,MAAK;4CACL,SAAS;4CACT,WAAU;;8DAEV,8OAAC,oMAAA,CAAA,QAAS;oDAAC,WAAU;;;;;;8DACrB,8OAAC;oDAAK,WAAU;8DAAwB;;;;;;;;;;;;;;;;;;8CAI5C,8OAAC;oCAAE,WAAU;8CAA6B;;;;;;;;;;;;sCAM5C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,MAAK;oCACL,SAAS,IAAM,OAAO,IAAI;oCAC1B,WAAU;8CACX;;;;;;8CAGD,8OAAC;oCACC,MAAK;oCACL,UAAU;oCACV,WAAU;8CAET,UAAU,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOvC", "debugId": null}}, {"offset": {"line": 575, "column": 0}, "map": {"version": 3, "file": "upload.js", "sources": ["file:///Users/<USER>/Desktop/rental/rental-properties/node_modules/lucide-react/src/icons/upload.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M12 3v12', key: '1x0j5s' }],\n  ['path', { d: 'm17 8-5-5-5 5', key: '7q97r8' }],\n  ['path', { d: 'M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4', key: 'ih7n3h' }],\n];\n\n/**\n * @component @name Upload\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTIgM3YxMiIgLz4KICA8cGF0aCBkPSJtMTcgOC01LTUtNSA1IiAvPgogIDxwYXRoIGQ9Ik0yMSAxNXY0YTIgMiAwIDAgMS0yIDJINWEyIDIgMCAwIDEtMi0ydi00IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/upload\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Upload = createLucideIcon('upload', __iconNode);\n\nexport default Upload;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACzC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC9C;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA6C,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CAC5E;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAS,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAU,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 628, "column": 0}, "map": {"version": 3, "file": "x.js", "sources": ["file:///Users/<USER>/Desktop/rental/rental-properties/node_modules/lucide-react/src/icons/x.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M18 6 6 18', key: '1bl5f8' }],\n  ['path', { d: 'm6 6 12 12', key: 'd8bk6v' }],\n];\n\n/**\n * @component @name X\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTggNiA2IDE4IiAvPgogIDxwYXRoIGQ9Im02IDYgMTIgMTIiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/x\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst X = createLucideIcon('x', __iconNode);\n\nexport default X;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAc,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC3C;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAc,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CAC7C;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAI,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,EAAK,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 674, "column": 0}, "map": {"version": 3, "file": "image.js", "sources": ["file:///Users/<USER>/Desktop/rental/rental-properties/node_modules/lucide-react/src/icons/image.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['rect', { width: '18', height: '18', x: '3', y: '3', rx: '2', ry: '2', key: '1m3agn' }],\n  ['circle', { cx: '9', cy: '9', r: '2', key: 'af1f0g' }],\n  ['path', { d: 'm21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21', key: '1xmnt7' }],\n];\n\n/**\n * @component @name Image\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cmVjdCB3aWR0aD0iMTgiIGhlaWdodD0iMTgiIHg9IjMiIHk9IjMiIHJ4PSIyIiByeT0iMiIgLz4KICA8Y2lyY2xlIGN4PSI5IiBjeT0iOSIgcj0iMiIgLz4KICA8cGF0aCBkPSJtMjEgMTUtMy4wODYtMy4wODZhMiAyIDAgMCAwLTIuODI4IDBMNiAyMSIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/image\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Image = createLucideIcon('image', __iconNode);\n\nexport default Image;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,KAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM;YAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAQ,CAAA,CAAA,CAAA,CAAA,CAAM,CAAA;YAAA,CAAA,EAAG,CAAK,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAG;YAAK,CAAI,CAAA,CAAA,CAAA,GAAA,CAAK;YAAA,CAAA,EAAI,CAAA,CAAA,CAAA,CAAK,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;IACvF;QAAC,QAAU,CAAA;QAAA,CAAA;YAAE,EAAI,CAAA,CAAA,CAAA,CAAA,CAAA;YAAK,CAAI,CAAA,CAAA,CAAA,GAAA,CAAK;YAAA,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA;YAAK,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;IACtD;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA6C,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CAC5E;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,CAAQ,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,EAAS,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}]}