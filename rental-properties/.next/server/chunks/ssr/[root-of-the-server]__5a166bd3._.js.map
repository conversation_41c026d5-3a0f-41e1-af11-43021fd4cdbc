{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/rental/rental-properties/src/components/DashboardNav.tsx"], "sourcesContent": ["'use client'\n\nimport { useAuth } from '@/contexts/AuthContext'\nimport { Home, List, Search, MessageCircle, Plus, User, LogOut } from 'lucide-react'\nimport Link from 'next/link'\nimport { usePathname } from 'next/navigation'\n\nexport default function DashboardNav() {\n  const { user, signOut } = useAuth()\n  const pathname = usePathname()\n\n  const handleSignOut = async () => {\n    await signOut()\n  }\n\n  const navItems = [\n    {\n      name: 'My Listings',\n      href: '/dashboard/my-listings',\n      icon: List,\n    },\n    {\n      name: 'Explore',\n      href: '/dashboard/explore',\n      icon: Search,\n    },\n    {\n      name: 'Cha<PERSON>',\n      href: '/dashboard/chats',\n      icon: MessageCircle,\n    },\n  ]\n\n  return (\n    <nav className=\"bg-white shadow-sm border-b\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between items-center h-16\">\n          {/* Logo */}\n          <Link href=\"/dashboard\" className=\"flex items-center\">\n            <Home className=\"h-8 w-8 text-blue-600\" />\n            <span className=\"ml-2 text-xl font-bold text-gray-900\">RentalHub</span>\n          </Link>\n\n          {/* Navigation Links */}\n          <div className=\"hidden md:flex items-center space-x-8\">\n            {navItems.map((item) => {\n              const Icon = item.icon\n              const isActive = pathname === item.href\n              return (\n                <Link\n                  key={item.name}\n                  href={item.href}\n                  className={`flex items-center px-3 py-2 rounded-md text-sm font-medium transition-colors ${\n                    isActive\n                      ? 'bg-blue-100 text-blue-700'\n                      : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'\n                  }`}\n                >\n                  <Icon className=\"h-4 w-4 mr-2\" />\n                  {item.name}\n                </Link>\n              )\n            })}\n          </div>\n\n          {/* Right side */}\n          <div className=\"flex items-center space-x-4\">\n            <Link\n              href=\"/dashboard/add-property\"\n              className=\"bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700 flex items-center\"\n            >\n              <Plus className=\"h-4 w-4 mr-2\" />\n              Add Property\n            </Link>\n            \n            <div className=\"flex items-center space-x-2\">\n              <User className=\"h-5 w-5 text-gray-600\" />\n              <span className=\"text-sm text-gray-700\">{user?.email}</span>\n            </div>\n            \n            <button\n              onClick={handleSignOut}\n              className=\"text-gray-600 hover:text-gray-900 flex items-center\"\n            >\n              <LogOut className=\"h-4 w-4\" />\n            </button>\n          </div>\n        </div>\n\n        {/* Mobile Navigation */}\n        <div className=\"md:hidden border-t border-gray-200\">\n          <div className=\"flex justify-around py-2\">\n            {navItems.map((item) => {\n              const Icon = item.icon\n              const isActive = pathname === item.href\n              return (\n                <Link\n                  key={item.name}\n                  href={item.href}\n                  className={`flex flex-col items-center px-3 py-2 text-xs font-medium transition-colors ${\n                    isActive\n                      ? 'text-blue-700'\n                      : 'text-gray-600 hover:text-gray-900'\n                  }`}\n                >\n                  <Icon className=\"h-5 w-5 mb-1\" />\n                  {item.name}\n                </Link>\n              )\n            })}\n          </div>\n        </div>\n      </div>\n    </nav>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AALA;;;;;;AAOe,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAChC,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,MAAM,gBAAgB;QACpB,MAAM;IACR;IAEA,MAAM,WAAW;QACf;YACE,MAAM;YACN,MAAM;YACN,MAAM,kMAAA,CAAA,OAAI;QACZ;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,sMAAA,CAAA,SAAM;QACd;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,wNAAA,CAAA,gBAAa;QACrB;KACD;IAED,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAa,WAAU;;8CAChC,8OAAC,mMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;8CAChB,8OAAC;oCAAK,WAAU;8CAAuC;;;;;;;;;;;;sCAIzD,8OAAC;4BAAI,WAAU;sCACZ,SAAS,GAAG,CAAC,CAAC;gCACb,MAAM,OAAO,KAAK,IAAI;gCACtB,MAAM,WAAW,aAAa,KAAK,IAAI;gCACvC,qBACE,8OAAC,4JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAW,CAAC,6EAA6E,EACvF,WACI,8BACA,uDACJ;;sDAEF,8OAAC;4CAAK,WAAU;;;;;;wCACf,KAAK,IAAI;;mCATL,KAAK,IAAI;;;;;4BAYpB;;;;;;sCAIF,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;;sDAEV,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAInC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;sDAChB,8OAAC;4CAAK,WAAU;sDAAyB,MAAM;;;;;;;;;;;;8CAGjD,8OAAC;oCACC,SAAS;oCACT,WAAU;8CAEV,cAAA,8OAAC,0MAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;8BAMxB,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACZ,SAAS,GAAG,CAAC,CAAC;4BACb,MAAM,OAAO,KAAK,IAAI;4BACtB,MAAM,WAAW,aAAa,KAAK,IAAI;4BACvC,qBACE,8OAAC,4JAAA,CAAA,UAAI;gCAEH,MAAM,KAAK,IAAI;gCACf,WAAW,CAAC,2EAA2E,EACrF,WACI,kBACA,qCACJ;;kDAEF,8OAAC;wCAAK,WAAU;;;;;;oCACf,KAAK,IAAI;;+BATL,KAAK,IAAI;;;;;wBAYpB;;;;;;;;;;;;;;;;;;;;;;AAMZ", "debugId": null}}, {"offset": {"line": 262, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/rental/rental-properties/src/app/dashboard/layout.tsx"], "sourcesContent": ["'use client'\n\nimport { useAuth } from '@/contexts/AuthContext'\nimport { useRouter } from 'next/navigation'\nimport { useEffect } from 'react'\nimport DashboardNav from '@/components/DashboardNav'\n\nexport default function DashboardLayout({\n  children,\n}: {\n  children: React.ReactNode\n}) {\n  const { user, loading } = useAuth()\n  const router = useRouter()\n\n  useEffect(() => {\n    if (!loading && !user) {\n      router.push('/')\n    }\n  }, [user, loading, router])\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600\"></div>\n      </div>\n    )\n  }\n\n  if (!user) {\n    return null\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <DashboardNav />\n      <main className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {children}\n      </main>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAOe,SAAS,gBAAgB,EACtC,QAAQ,EAGT;IACC,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAChC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,WAAW,CAAC,MAAM;YACrB,OAAO,IAAI,CAAC;QACd;IACF,GAAG;QAAC;QAAM;QAAS;KAAO;IAE1B,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,IAAI,CAAC,MAAM;QACT,OAAO;IACT;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,kIAAA,CAAA,UAAY;;;;;0BACb,8OAAC;gBAAK,WAAU;0BACb;;;;;;;;;;;;AAIT", "debugId": null}}]}