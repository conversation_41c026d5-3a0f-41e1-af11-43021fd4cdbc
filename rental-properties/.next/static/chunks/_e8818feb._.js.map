{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/rental/rental-properties/src/lib/supabase.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js'\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!\n\nexport const supabase = createClient(supabaseUrl, supabaseAnonKey)\n\nexport type Database = {\n  public: {\n    Tables: {\n      users: {\n        Row: {\n          id: string\n          email: string\n          full_name: string | null\n          avatar_url: string | null\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          id: string\n          email: string\n          full_name?: string | null\n          avatar_url?: string | null\n          created_at?: string\n          updated_at?: string\n        }\n        Update: {\n          id?: string\n          email?: string\n          full_name?: string | null\n          avatar_url?: string | null\n          created_at?: string\n          updated_at?: string\n        }\n      }\n      properties: {\n        Row: {\n          id: string\n          user_id: string\n          title: string\n          description: string | null\n          area: string\n          price: number\n          size_sqft: number | null\n          images: string[]\n          view_count: number\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          id?: string\n          user_id: string\n          title: string\n          description?: string | null\n          area: string\n          price: number\n          size_sqft?: number | null\n          images?: string[]\n          view_count?: number\n          created_at?: string\n          updated_at?: string\n        }\n        Update: {\n          id?: string\n          user_id?: string\n          title?: string\n          description?: string | null\n          area?: string\n          price?: number\n          size_sqft?: number | null\n          images?: string[]\n          view_count?: number\n          created_at?: string\n          updated_at?: string\n        }\n      }\n      chats: {\n        Row: {\n          id: string\n          property_id: string\n          buyer_id: string\n          seller_id: string\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          id?: string\n          property_id: string\n          buyer_id: string\n          seller_id: string\n          created_at?: string\n          updated_at?: string\n        }\n        Update: {\n          id?: string\n          property_id?: string\n          buyer_id?: string\n          seller_id?: string\n          created_at?: string\n          updated_at?: string\n        }\n      }\n      messages: {\n        Row: {\n          id: string\n          chat_id: string\n          sender_id: string\n          content: string\n          created_at: string\n        }\n        Insert: {\n          id?: string\n          chat_id: string\n          sender_id: string\n          content: string\n          created_at?: string\n        }\n        Update: {\n          id?: string\n          chat_id?: string\n          sender_id?: string\n          content?: string\n          created_at?: string\n        }\n      }\n      property_views: {\n        Row: {\n          id: string\n          property_id: string\n          user_id: string | null\n          ip_address: string | null\n          created_at: string\n        }\n        Insert: {\n          id?: string\n          property_id: string\n          user_id?: string | null\n          ip_address?: string | null\n          created_at?: string\n        }\n        Update: {\n          id?: string\n          property_id?: string\n          user_id?: string | null\n          ip_address?: string | null\n          created_at?: string\n        }\n      }\n    }\n  }\n}\n"], "names": [], "mappings": ";;;AAEoB;AAFpB;;AAEA,MAAM;AACN,MAAM;AAEC,MAAM,WAAW,CAAA,GAAA,0LAAA,CAAA,eAAY,AAAD,EAAE,aAAa", "debugId": null}}, {"offset": {"line": 25, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/rental/rental-properties/src/app/dashboard/add-property/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { useAuth } from '@/contexts/AuthContext'\nimport { supabase } from '@/lib/supabase'\nimport { useRouter } from 'next/navigation'\nimport { Upload, X, Plus } from 'lucide-react'\nimport toast from 'react-hot-toast'\n\nexport default function AddProperty() {\n  const { user } = useAuth()\n  const router = useRouter()\n  const [loading, setLoading] = useState(false)\n  const [formData, setFormData] = useState({\n    title: '',\n    description: '',\n    area: '',\n    price: '',\n    size_sqft: '',\n    images: [] as string[],\n  })\n\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {\n    const { name, value } = e.target\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }))\n  }\n\n  const handleImageAdd = () => {\n    const imageUrl = prompt('Enter image URL:')\n    if (imageUrl && imageUrl.trim()) {\n      setFormData(prev => ({\n        ...prev,\n        images: [...prev.images, imageUrl.trim()]\n      }))\n    }\n  }\n\n  const handleImageRemove = (index: number) => {\n    setFormData(prev => ({\n      ...prev,\n      images: prev.images.filter((_, i) => i !== index)\n    }))\n  }\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault()\n    \n    if (!user) {\n      toast.error('You must be logged in to add a property')\n      return\n    }\n\n    if (!formData.title || !formData.area || !formData.price) {\n      toast.error('Please fill in all required fields')\n      return\n    }\n\n    setLoading(true)\n    try {\n      const { error } = await supabase\n        .from('properties')\n        .insert({\n          user_id: user.id,\n          title: formData.title,\n          description: formData.description || null,\n          area: formData.area,\n          price: parseFloat(formData.price),\n          size_sqft: formData.size_sqft ? parseInt(formData.size_sqft) : null,\n          images: formData.images,\n        })\n\n      if (error) throw error\n\n      toast.success('Property added successfully!')\n      router.push('/dashboard/my-listings')\n    } catch (error) {\n      console.error('Error adding property:', error)\n      toast.error('Failed to add property')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  return (\n    <div className=\"max-w-2xl mx-auto\">\n      <div className=\"bg-white rounded-lg shadow p-6\">\n        <h1 className=\"text-2xl font-bold text-gray-900 mb-6\">Add New Property</h1>\n        \n        <form onSubmit={handleSubmit} className=\"space-y-6\">\n          {/* Title */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              Property Title *\n            </label>\n            <input\n              type=\"text\"\n              name=\"title\"\n              value={formData.title}\n              onChange={handleInputChange}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n              placeholder=\"e.g., Beautiful 2BR Apartment in Downtown\"\n              required\n            />\n          </div>\n\n          {/* Area */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              Area/Location *\n            </label>\n            <input\n              type=\"text\"\n              name=\"area\"\n              value={formData.area}\n              onChange={handleInputChange}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n              placeholder=\"e.g., Downtown, Manhattan, NY\"\n              required\n            />\n          </div>\n\n          {/* Price and Size */}\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Monthly Rent ($) *\n              </label>\n              <input\n                type=\"number\"\n                name=\"price\"\n                value={formData.price}\n                onChange={handleInputChange}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                placeholder=\"2500\"\n                min=\"0\"\n                step=\"0.01\"\n                required\n              />\n            </div>\n            \n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Size (sq ft)\n              </label>\n              <input\n                type=\"number\"\n                name=\"size_sqft\"\n                value={formData.size_sqft}\n                onChange={handleInputChange}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                placeholder=\"1200\"\n                min=\"0\"\n              />\n            </div>\n          </div>\n\n          {/* Description */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              Description\n            </label>\n            <textarea\n              name=\"description\"\n              value={formData.description}\n              onChange={handleInputChange}\n              rows={4}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n              placeholder=\"Describe your property, amenities, nearby attractions, etc.\"\n            />\n          </div>\n\n          {/* Images */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              Property Images\n            </label>\n            \n            {/* Image List */}\n            {formData.images.length > 0 && (\n              <div className=\"grid grid-cols-2 md:grid-cols-3 gap-4 mb-4\">\n                {formData.images.map((image, index) => (\n                  <div key={index} className=\"relative\">\n                    <img\n                      src={image}\n                      alt={`Property ${index + 1}`}\n                      className=\"w-full h-24 object-cover rounded-md border\"\n                    />\n                    <button\n                      type=\"button\"\n                      onClick={() => handleImageRemove(index)}\n                      className=\"absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600\"\n                    >\n                      <X className=\"h-3 w-3\" />\n                    </button>\n                  </div>\n                ))}\n              </div>\n            )}\n            \n            {/* Add Image Button */}\n            <button\n              type=\"button\"\n              onClick={handleImageAdd}\n              className=\"flex items-center justify-center w-full h-24 border-2 border-dashed border-gray-300 rounded-md hover:border-gray-400 transition-colors\"\n            >\n              <div className=\"text-center\">\n                <Plus className=\"h-6 w-6 text-gray-400 mx-auto mb-1\" />\n                <span className=\"text-sm text-gray-600\">Add Image URL</span>\n              </div>\n            </button>\n            <p className=\"text-xs text-gray-500 mt-1\">\n              Click to add image URLs. You can add multiple images.\n            </p>\n          </div>\n\n          {/* Submit Button */}\n          <div className=\"flex justify-end space-x-4\">\n            <button\n              type=\"button\"\n              onClick={() => router.back()}\n              className=\"px-4 py-2 text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300\"\n            >\n              Cancel\n            </button>\n            <button\n              type=\"submit\"\n              disabled={loading}\n              className=\"px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50\"\n            >\n              {loading ? 'Adding...' : 'Add Property'}\n            </button>\n          </div>\n        </form>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AACA;;;AAPA;;;;;;;AASe,SAAS;;IACtB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IACvB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,OAAO;QACP,aAAa;QACb,MAAM;QACN,OAAO;QACP,WAAW;QACX,QAAQ,EAAE;IACZ;IAEA,MAAM,oBAAoB,CAAC;QACzB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;QAChC,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,KAAK,EAAE;YACV,CAAC;IACH;IAEA,MAAM,iBAAiB;QACrB,MAAM,WAAW,OAAO;QACxB,IAAI,YAAY,SAAS,IAAI,IAAI;YAC/B,YAAY,CAAA,OAAQ,CAAC;oBACnB,GAAG,IAAI;oBACP,QAAQ;2BAAI,KAAK,MAAM;wBAAE,SAAS,IAAI;qBAAG;gBAC3C,CAAC;QACH;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,QAAQ,KAAK,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,IAAM,MAAM;YAC7C,CAAC;IACH;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI,CAAC,MAAM;YACT,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,IAAI,CAAC,SAAS,KAAK,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,KAAK,EAAE;YACxD,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,WAAW;QACX,IAAI;YACF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAC7B,IAAI,CAAC,cACL,MAAM,CAAC;gBACN,SAAS,KAAK,EAAE;gBAChB,OAAO,SAAS,KAAK;gBACrB,aAAa,SAAS,WAAW,IAAI;gBACrC,MAAM,SAAS,IAAI;gBACnB,OAAO,WAAW,SAAS,KAAK;gBAChC,WAAW,SAAS,SAAS,GAAG,SAAS,SAAS,SAAS,IAAI;gBAC/D,QAAQ,SAAS,MAAM;YACzB;YAEF,IAAI,OAAO,MAAM;YAEjB,0JAAA,CAAA,UAAK,CAAC,OAAO,CAAC;YACd,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,WAAW;QACb;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAG,WAAU;8BAAwC;;;;;;8BAEtD,6LAAC;oBAAK,UAAU;oBAAc,WAAU;;sCAEtC,6LAAC;;8CACC,6LAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,6LAAC;oCACC,MAAK;oCACL,MAAK;oCACL,OAAO,SAAS,KAAK;oCACrB,UAAU;oCACV,WAAU;oCACV,aAAY;oCACZ,QAAQ;;;;;;;;;;;;sCAKZ,6LAAC;;8CACC,6LAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,6LAAC;oCACC,MAAK;oCACL,MAAK;oCACL,OAAO,SAAS,IAAI;oCACpB,UAAU;oCACV,WAAU;oCACV,aAAY;oCACZ,QAAQ;;;;;;;;;;;;sCAKZ,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAGhE,6LAAC;4CACC,MAAK;4CACL,MAAK;4CACL,OAAO,SAAS,KAAK;4CACrB,UAAU;4CACV,WAAU;4CACV,aAAY;4CACZ,KAAI;4CACJ,MAAK;4CACL,QAAQ;;;;;;;;;;;;8CAIZ,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAGhE,6LAAC;4CACC,MAAK;4CACL,MAAK;4CACL,OAAO,SAAS,SAAS;4CACzB,UAAU;4CACV,WAAU;4CACV,aAAY;4CACZ,KAAI;;;;;;;;;;;;;;;;;;sCAMV,6LAAC;;8CACC,6LAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,6LAAC;oCACC,MAAK;oCACL,OAAO,SAAS,WAAW;oCAC3B,UAAU;oCACV,MAAM;oCACN,WAAU;oCACV,aAAY;;;;;;;;;;;;sCAKhB,6LAAC;;8CACC,6LAAC;oCAAM,WAAU;8CAA+C;;;;;;gCAK/D,SAAS,MAAM,CAAC,MAAM,GAAG,mBACxB,6LAAC;oCAAI,WAAU;8CACZ,SAAS,MAAM,CAAC,GAAG,CAAC,CAAC,OAAO,sBAC3B,6LAAC;4CAAgB,WAAU;;8DACzB,6LAAC;oDACC,KAAK;oDACL,KAAK,CAAC,SAAS,EAAE,QAAQ,GAAG;oDAC5B,WAAU;;;;;;8DAEZ,6LAAC;oDACC,MAAK;oDACL,SAAS,IAAM,kBAAkB;oDACjC,WAAU;8DAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;wDAAC,WAAU;;;;;;;;;;;;2CAXP;;;;;;;;;;8CAmBhB,6LAAC;oCACC,MAAK;oCACL,SAAS;oCACT,WAAU;8CAEV,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAChB,6LAAC;gDAAK,WAAU;0DAAwB;;;;;;;;;;;;;;;;;8CAG5C,6LAAC;oCAAE,WAAU;8CAA6B;;;;;;;;;;;;sCAM5C,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,MAAK;oCACL,SAAS,IAAM,OAAO,IAAI;oCAC1B,WAAU;8CACX;;;;;;8CAGD,6LAAC;oCACC,MAAK;oCACL,UAAU;oCACV,WAAU;8CAET,UAAU,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOvC;GAtOwB;;QACL,kIAAA,CAAA,UAAO;QACT,qIAAA,CAAA,YAAS;;;KAFF", "debugId": null}}, {"offset": {"line": 449, "column": 0}, "map": {"version": 3, "file": "x.js", "sources": ["file:///Users/<USER>/Desktop/rental/rental-properties/node_modules/lucide-react/src/icons/x.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M18 6 6 18', key: '1bl5f8' }],\n  ['path', { d: 'm6 6 12 12', key: 'd8bk6v' }],\n];\n\n/**\n * @component @name X\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTggNiA2IDE4IiAvPgogIDxwYXRoIGQ9Im02IDYgMTIgMTIiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/x\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst X = createLucideIcon('x', __iconNode);\n\nexport default X;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAc,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC3C;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAc,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CAC7C;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAI,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,EAAK,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}]}