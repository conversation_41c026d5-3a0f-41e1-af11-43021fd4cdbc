'use client'

import { useState, useEffect } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { supabase } from '@/lib/supabase'
import { MessageCircle, User, Clock } from 'lucide-react'
import Link from 'next/link'

interface Chat {
  id: string
  property_id: string
  buyer_id: string
  seller_id: string
  created_at: string
  updated_at: string
  property: {
    title: string
    price: number
    images: string[]
  }
  buyer: {
    full_name: string | null
    email: string
  }
  seller: {
    full_name: string | null
    email: string
  }
  latest_message?: {
    content: string
    created_at: string
    sender_id: string
  }
}

export default function Chats() {
  const { user } = useAuth()
  const [chats, setChats] = useState<Chat[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    if (user) {
      fetchChats()
    }
  }, [user])

  const fetchChats = async () => {
    try {
      const { data } = await supabase
        .from('chats')
        .select(`
          *,
          property:properties!chats_property_id_fkey(title, price, images),
          buyer:users!chats_buyer_id_fkey(full_name, email),
          seller:users!chats_seller_id_fkey(full_name, email)
        `)
        .or(`buyer_id.eq.${user?.id},seller_id.eq.${user?.id}`)
        .order('updated_at', { ascending: false })

      if (data) {
        // Fetch latest message for each chat
        const chatsWithMessages = await Promise.all(
          data.map(async (chat) => {
            const { data: latestMessage } = await supabase
              .from('messages')
              .select('content, created_at, sender_id')
              .eq('chat_id', chat.id)
              .order('created_at', { ascending: false })
              .limit(1)
              .single()

            return {
              ...chat,
              latest_message: latestMessage
            }
          })
        )

        setChats(chatsWithMessages)
      }
    } catch (error) {
      console.error('Error fetching chats:', error)
    } finally {
      setLoading(false)
    }
  }

  const formatTime = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60)

    if (diffInHours < 24) {
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
    } else if (diffInHours < 24 * 7) {
      return date.toLocaleDateString([], { weekday: 'short' })
    } else {
      return date.toLocaleDateString([], { month: 'short', day: 'numeric' })
    }
  }

  const getOtherUser = (chat: Chat) => {
    return chat.buyer_id === user?.id ? chat.seller : chat.buyer
  }

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(price)
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900 mb-2">Chats</h1>
        <p className="text-gray-600">Your conversations with property owners and interested renters</p>
      </div>

      {/* Chats List */}
      {chats.length === 0 ? (
        <div className="bg-white rounded-lg shadow p-12 text-center">
          <MessageCircle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No chats yet</h3>
          <p className="text-gray-600 mb-6">
            Start exploring properties and contact owners to begin conversations.
          </p>
          <Link
            href="/dashboard/explore"
            className="bg-blue-600 text-white px-6 py-3 rounded-md hover:bg-blue-700 inline-flex items-center"
          >
            Explore Properties
          </Link>
        </div>
      ) : (
        <div className="bg-white rounded-lg shadow divide-y divide-gray-200">
          {chats.map((chat) => {
            const otherUser = getOtherUser(chat)
            const isFromCurrentUser = chat.latest_message?.sender_id === user?.id
            
            return (
              <Link
                key={chat.id}
                href={`/dashboard/chats/${chat.id}`}
                className="block hover:bg-gray-50 transition-colors"
              >
                <div className="p-6">
                  <div className="flex items-start space-x-4">
                    {/* Property Image */}
                    <div className="flex-shrink-0">
                      {chat.property.images && chat.property.images.length > 0 ? (
                        <img
                          src={chat.property.images[0]}
                          alt={chat.property.title}
                          className="w-16 h-16 rounded-lg object-cover"
                        />
                      ) : (
                        <div className="w-16 h-16 bg-gray-200 rounded-lg flex items-center justify-center">
                          <MessageCircle className="h-6 w-6 text-gray-400" />
                        </div>
                      )}
                    </div>

                    {/* Chat Info */}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between mb-1">
                        <h3 className="text-sm font-medium text-gray-900 truncate">
                          {chat.property.title}
                        </h3>
                        <span className="text-sm font-medium text-green-600">
                          {formatPrice(chat.property.price)}/month
                        </span>
                      </div>
                      
                      <div className="flex items-center text-sm text-gray-600 mb-2">
                        <User className="h-4 w-4 mr-1" />
                        <span>
                          {chat.buyer_id === user?.id ? 'Owner: ' : 'Interested: '}
                          {otherUser.full_name || otherUser.email}
                        </span>
                      </div>

                      {chat.latest_message && (
                        <div className="flex items-center justify-between">
                          <p className="text-sm text-gray-600 truncate">
                            {isFromCurrentUser ? 'You: ' : ''}
                            {chat.latest_message.content}
                          </p>
                          <div className="flex items-center text-xs text-gray-500 ml-2">
                            <Clock className="h-3 w-3 mr-1" />
                            {formatTime(chat.latest_message.created_at)}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </Link>
            )
          })}
        </div>
      )}
    </div>
  )
}
